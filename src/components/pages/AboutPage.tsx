import { useEffect } from 'react';
import SEO from '../ui/SEO';

interface AboutPageProps {}

export default function AboutPage({}: AboutPageProps) {
  return (
    <>
      <SEO 
        title="About" 
        description="Learn more about our photo album gallery website and how it can help you showcase your memories."
      />
      
      <div className="max-w-3xl mx-auto">
        <h1 className="text-3xl font-bold mb-6">About PhotoAlbum Gallery</h1>
        
        <div className="prose dark:prose-invert max-w-none">
          <p className="text-lg mb-4">
            PhotoAlbum Gallery is a minimalist, elegant solution for showcasing your precious memories.
            Built with performance and aesthetics in mind, our gallery provides a seamless experience
            for both photographers and viewers alike.
          </p>
          
          <h2 className="text-2xl font-semibold mt-8 mb-4">Our Philosophy</h2>
          <p className="mb-4">
            We believe that your photos should be the star of the show. That's why we've designed
            our gallery with a clean, minimalist aesthetic that puts your images front and center.
            No distractions, just beautiful memories.
          </p>
          
          <h2 className="text-2xl font-semibold mt-8 mb-4">Features</h2>
          <ul className="space-y-2 mb-6">
            <li className="flex items-start">
              <span className="mr-2">•</span>
              <span>Clean, minimalist design with white background and soft shadows</span>
            </li>
            <li className="flex items-start">
              <span className="mr-2">•</span>
              <span>Responsive layout that works beautifully on all devices</span>
            </li>
            <li className="flex items-start">
              <span className="mr-2">•</span>
              <span>Full-screen lightbox view for immersive photo browsing</span>
            </li>
            <li className="flex items-start">
              <span className="mr-2">•</span>
              <span>Smart search and filter functionality</span>
            </li>
            <li className="flex items-start">
              <span className="mr-2">•</span>
              <span>Light and dark mode for comfortable viewing in any environment</span>
            </li>
            <li className="flex items-start">
              <span className="mr-2">•</span>
              <span>Optimized image loading for fast performance</span>
            </li>
          </ul>
          
          <h2 className="text-2xl font-semibold mt-8 mb-4">Contact Us</h2>
          <p>
            Have questions or feedback? We'd love to hear from you. Reach out to us at 
            <a href="mailto:<EMAIL>" className="text-primary ml-1">
              <EMAIL>
            </a>
          </p>
        </div>
      </div>
    </>
  );
}
