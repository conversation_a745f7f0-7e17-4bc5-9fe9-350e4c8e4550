import { useState } from 'react';
import AlbumCard from '../gallery/AlbumCard';

// Mock data for featured albums
const featuredAlbums = [
  {
    id: 'summer-vacation',
    title: 'Summer Vacation',
    coverImage: '/placeholder-images/summer.jpg',
    photoCount: 24,
    date: 'June 2024'
  },
  {
    id: 'city-lights',
    title: 'City Lights',
    coverImage: '/placeholder-images/city.jpg',
    photoCount: 18,
    date: 'May 2024'
  },
  {
    id: 'nature-walks',
    title: 'Nature Walks',
    coverImage: '/placeholder-images/nature.jpg',
    photoCount: 32,
    date: 'April 2024'
  },
  {
    id: 'family-gathering',
    title: 'Family Gathering',
    coverImage: '/placeholder-images/family.jpg',
    photoCount: 45,
    date: 'March 2024'
  }
];

export default function HomePage() {
  return (
    <div className="space-y-12">
      {/* Hero section */}
      <section className="py-12 md:py-20 text-center">
        <h1 className="text-4xl md:text-5xl font-bold mb-6">Capture Your Memories</h1>
        <p className="text-xl md:text-2xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
          A beautiful, minimalist gallery to showcase your precious moments and share them with the world.
        </p>
      </section>

      {/* Featured albums section */}
      <section>
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-semibold">Featured Albums</h2>
          <a href="/albums" className="text-primary hover:underline">View all albums</a>
        </div>
        <div className="gallery-grid">
          {featuredAlbums.map((album) => (
            <AlbumCard
              key={album.id}
              id={album.id}
              title={album.title}
              coverImage={album.coverImage}
              photoCount={album.photoCount}
              date={album.date}
            />
          ))}
        </div>
      </section>

      {/* About section */}
      <section className="bg-gray-50 dark:bg-gray-800 p-8 rounded-lg">
        <div className="max-w-3xl mx-auto">
          <h2 className="text-2xl font-semibold mb-4">About This Gallery</h2>
          <p className="text-gray-600 dark:text-gray-300 mb-4">
            This photo album gallery is designed to showcase your memories in an elegant, 
            minimalist style. With a clean interface and smooth navigation, your photos 
            will be the center of attention.
          </p>
          <p className="text-gray-600 dark:text-gray-300">
            Organize your photos into albums, add captions and dates, and enjoy a 
            beautiful full-screen viewing experience. The gallery is fully responsive 
            and works perfectly on all your devices.
          </p>
        </div>
      </section>
    </div>
  );
}
