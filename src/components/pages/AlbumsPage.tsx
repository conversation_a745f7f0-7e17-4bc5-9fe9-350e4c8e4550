import { useState } from 'react';
import AlbumCard from '../gallery/AlbumCard';
import AlbumFilter from '../gallery/AlbumFilter';

// Mock data for all albums
const allAlbums = [
  {
    id: 'summer-vacation',
    title: 'Summer Vacation',
    coverImage: '/placeholder-images/summer.jpg',
    photoCount: 24,
    date: 'June 2024',
    category: 'Travel'
  },
  {
    id: 'city-lights',
    title: 'City Lights',
    coverImage: '/placeholder-images/city.jpg',
    photoCount: 18,
    date: 'May 2024',
    category: 'Urban'
  },
  {
    id: 'nature-walks',
    title: 'Nature Walks',
    coverImage: '/placeholder-images/nature.jpg',
    photoCount: 32,
    date: 'April 2024',
    category: 'Nature'
  },
  {
    id: 'family-gathering',
    title: 'Family Gathering',
    coverImage: '/placeholder-images/family.jpg',
    photoCount: 45,
    date: 'March 2024',
    category: 'Family'
  },
  {
    id: 'winter-wonderland',
    title: 'Winter Wonderland',
    coverImage: '/placeholder-images/winter.jpg',
    photoCount: 27,
    date: 'January 2024',
    category: 'Travel'
  },
  {
    id: 'pet-moments',
    title: 'Pet Moments',
    coverImage: '/placeholder-images/pets.jpg',
    photoCount: 19,
    date: 'February 2024',
    category: 'Pets'
  }
];

export default function AlbumsPage() {
  const [filteredAlbums, setFilteredAlbums] = useState(allAlbums);
  const [searchQuery, setSearchQuery] = useState('');
  const [dateFilter, setDateFilter] = useState('');
  
  // Extract unique dates for filter dropdown
  const availableDates = [...new Set(allAlbums.map(album => album.date))];
  
  // Handle search
  const handleSearch = (query: string) => {
    setSearchQuery(query);
    applyFilters(query, dateFilter);
  };
  
  // Handle date filter
  const handleDateFilter = (date: string) => {
    setDateFilter(date);
    applyFilters(searchQuery, date);
  };
  
  // Apply both filters
  const applyFilters = (query: string, date: string) => {
    let results = allAlbums;
    
    // Apply search query filter
    if (query) {
      const lowercaseQuery = query.toLowerCase();
      results = results.filter(album => 
        album.title.toLowerCase().includes(lowercaseQuery) || 
        album.category.toLowerCase().includes(lowercaseQuery)
      );
    }
    
    // Apply date filter
    if (date) {
      results = results.filter(album => album.date === date);
    }
    
    setFilteredAlbums(results);
  };

  return (
    <div>
      <h1 className="text-3xl font-bold mb-6">Photo Albums</h1>
      
      {/* Search and filter */}
      <AlbumFilter 
        onSearch={handleSearch}
        onFilterByDate={handleDateFilter}
        availableDates={availableDates}
      />
      
      {/* Results count */}
      <p className="text-sm text-gray-500 dark:text-gray-400 mb-6">
        Showing {filteredAlbums.length} of {allAlbums.length} albums
      </p>
      
      {/* Albums grid */}
      {filteredAlbums.length > 0 ? (
        <div className="gallery-grid">
          {filteredAlbums.map((album) => (
            <AlbumCard
              key={album.id}
              id={album.id}
              title={album.title}
              coverImage={album.coverImage}
              photoCount={album.photoCount}
              date={album.date}
            />
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <p className="text-gray-600 dark:text-gray-300">
            No albums found matching your search criteria.
          </p>
        </div>
      )}
    </div>
  );
}
