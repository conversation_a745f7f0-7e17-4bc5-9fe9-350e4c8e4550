import { useState, useEffect } from 'react';
import AlbumCard from '../gallery/AlbumCard';
import AlbumFilter from '../gallery/AlbumFilter';
import { useAdmin } from '../../contexts/AdminContext';
import SEO from '../ui/SEO';

export default function AlbumsPage() {
  const { albums } = useAdmin();
  const [filteredAlbums, setFilteredAlbums] = useState(albums);
  const [searchQuery, setSearchQuery] = useState('');
  const [dateFilter, setDateFilter] = useState('');

  // Update filtered albums when albums change
  useEffect(() => {
    setFilteredAlbums(albums);
  }, [albums]);

  // Extract unique dates for filter dropdown
  const availableDates = [...new Set(albums.map(album => album.date))];
  
  // Handle search
  const handleSearch = (query: string) => {
    setSearchQuery(query);
    applyFilters(query, dateFilter);
  };
  
  // Handle date filter
  const handleDateFilter = (date: string) => {
    setDateFilter(date);
    applyFilters(searchQuery, date);
  };
  
  // Apply both filters
  const applyFilters = (query: string, date: string) => {
    let results = albums;
    
    // Apply search query filter
    if (query) {
      const lowercaseQuery = query.toLowerCase();
      results = results.filter(album => 
        album.title.toLowerCase().includes(lowercaseQuery) || 
        album.category.toLowerCase().includes(lowercaseQuery)
      );
    }
    
    // Apply date filter
    if (date) {
      results = results.filter(album => album.date === date);
    }
    
    setFilteredAlbums(results);
  };

  return (
    <div>
      <SEO
        title="Photo Albums"
        description="Browse through our collection of photo albums featuring travel, family, nature, and more."
      />
      <h1 className="text-3xl font-bold mb-6">Photo Albums</h1>
      
      {/* Search and filter */}
      <AlbumFilter 
        onSearch={handleSearch}
        onFilterByDate={handleDateFilter}
        availableDates={availableDates}
      />
      
      {/* Results count */}
      <p className="text-sm text-gray-500 dark:text-gray-400 mb-6">
        Showing {filteredAlbums.length} of {albums.length} albums
      </p>
      
      {/* Albums grid */}
      {filteredAlbums.length > 0 ? (
        <div className="gallery-grid">
          {filteredAlbums.map((album) => (
            <AlbumCard
              key={album.id}
              id={album.id}
              title={album.title}
              coverImage={album.coverImage}
              photoCount={album.photoCount}
              date={album.date}
            />
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <p className="text-gray-600 dark:text-gray-300">
            No albums found matching your search criteria.
          </p>
        </div>
      )}
    </div>
  );
}
