import { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import Lightbox from '../gallery/Lightbox';
import LazyImage from '../ui/LazyImage';

// Mock data for album details
const albumsData = {
  'summer-vacation': {
    id: 'summer-vacation',
    title: 'Summer Vacation',
    description: 'Memories from our amazing summer trip to the coast.',
    date: 'June 2024',
    photos: [
      {
        id: 'photo-1',
        src: '/placeholder-images/summer-1.jpg',
        caption: 'Sunset at the beach',
        date: 'June 15, 2024'
      },
      {
        id: 'photo-2',
        src: '/placeholder-images/summer-2.jpg',
        caption: 'Family picnic by the shore',
        date: 'June 16, 2024'
      },
      {
        id: 'photo-3',
        src: '/placeholder-images/summer-3.jpg',
        caption: 'Swimming in the ocean',
        date: 'June 17, 2024'
      },
      {
        id: 'photo-4',
        src: '/placeholder-images/summer-4.jpg',
        caption: 'Beach volleyball tournament',
        date: 'June 18, 2024'
      },
      {
        id: 'photo-5',
        src: '/placeholder-images/summer-5.jpg',
        caption: 'Seafood dinner at the resort',
        date: 'June 19, 2024'
      },
      {
        id: 'photo-6',
        src: '/placeholder-images/summer-6.jpg',
        caption: 'Hiking the coastal trail',
        date: 'June 20, 2024'
      }
    ]
  },
  'city-lights': {
    id: 'city-lights',
    title: 'City Lights',
    description: 'Urban exploration and nighttime photography in the downtown area.',
    date: 'May 2024',
    photos: [
      {
        id: 'photo-1',
        src: '/placeholder-images/city-1.jpg',
        caption: 'Downtown skyline at dusk',
        date: 'May 5, 2024'
      },
      {
        id: 'photo-2',
        src: '/placeholder-images/city-2.jpg',
        caption: 'Street performers in the plaza',
        date: 'May 6, 2024'
      },
      {
        id: 'photo-3',
        src: '/placeholder-images/city-3.jpg',
        caption: 'Neon lights on Main Street',
        date: 'May 7, 2024'
      },
      {
        id: 'photo-4',
        src: '/placeholder-images/city-4.jpg',
        caption: 'Rooftop bar view',
        date: 'May 8, 2024'
      }
    ]
  }
};

export default function AlbumPage() {
  const { albumId } = useParams<{ albumId: string }>();
  const [lightboxOpen, setLightboxOpen] = useState(false);
  const [currentPhotoIndex, setCurrentPhotoIndex] = useState(0);
  
  // Fallback if album not found
  if (!albumId || !albumsData[albumId]) {
    return (
      <div className="text-center py-12">
        <h2 className="text-2xl font-semibold mb-4">Album Not Found</h2>
        <p className="text-gray-600 dark:text-gray-300 mb-6">
          The album you're looking for doesn't exist or has been removed.
        </p>
        <a href="/" className="text-primary hover:underline">
          Return to homepage
        </a>
      </div>
    );
  }
  
  const album = albumsData[albumId];
  
  const openLightbox = (index: number) => {
    setCurrentPhotoIndex(index);
    setLightboxOpen(true);
  };
  
  const closeLightbox = () => {
    setLightboxOpen(false);
  };
  
  const goToPrevPhoto = () => {
    setCurrentPhotoIndex((prevIndex) => 
      prevIndex > 0 ? prevIndex - 1 : prevIndex
    );
  };
  
  const goToNextPhoto = () => {
    setCurrentPhotoIndex((prevIndex) => 
      prevIndex < album.photos.length - 1 ? prevIndex + 1 : prevIndex
    );
  };

  return (
    <div>
      {/* Album header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">{album.title}</h1>
        <p className="text-gray-600 dark:text-gray-300 mb-2">{album.description}</p>
        <p className="text-sm text-gray-500 dark:text-gray-400">{album.date} • {album.photos.length} photos</p>
      </div>
      
      {/* Photo grid */}
      <div className="gallery-grid">
        {album.photos.map((photo, index) => (
          <div 
            key={photo.id} 
            className="album-card cursor-pointer"
            onClick={() => openLightbox(index)}
          >
            <div className="relative overflow-hidden">
              <LazyImage 
                src={photo.src} 
                alt={photo.caption} 
                className="photo-thumbnail"
              />
            </div>
            <div className="p-3">
              <p className="text-sm font-medium truncate">{photo.caption}</p>
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">{photo.date}</p>
            </div>
          </div>
        ))}
      </div>
      
      {/* Lightbox */}
      {lightboxOpen && (
        <Lightbox 
          photos={album.photos}
          currentIndex={currentPhotoIndex}
          onClose={closeLightbox}
          onPrev={goToPrevPhoto}
          onNext={goToNextPhoto}
        />
      )}
    </div>
  );
}
