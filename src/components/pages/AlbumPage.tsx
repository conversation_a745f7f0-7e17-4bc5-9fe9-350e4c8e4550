import { useState } from 'react';
import { useParams } from 'react-router-dom';
import Lightbox from '../gallery/Lightbox';
import LazyImage from '../ui/LazyImage';
import { useAdmin } from '../../contexts/AdminContext';
import SEO from '../ui/SEO';

export default function AlbumPage() {
  const { albumId } = useParams<{ albumId: string }>();
  const { getAlbum } = useAdmin();
  const [lightboxOpen, setLightboxOpen] = useState(false);
  const [currentPhotoIndex, setCurrentPhotoIndex] = useState(0);

  const album = albumId ? getAlbum(albumId) : null;

  // Fallback if album not found
  if (!albumId || !album) {
    return (
      <div className="text-center py-12">
        <h2 className="text-2xl font-semibold mb-4">Album Not Found</h2>
        <p className="text-gray-600 dark:text-gray-300 mb-6">
          The album you're looking for doesn't exist or has been removed.
        </p>
        <a href="/" className="text-primary hover:underline">
          Return to homepage
        </a>
      </div>
    );
  }
  
  const openLightbox = (index: number) => {
    setCurrentPhotoIndex(index);
    setLightboxOpen(true);
  };
  
  const closeLightbox = () => {
    setLightboxOpen(false);
  };
  
  const goToPrevPhoto = () => {
    setCurrentPhotoIndex((prevIndex) => 
      prevIndex > 0 ? prevIndex - 1 : prevIndex
    );
  };
  
  const goToNextPhoto = () => {
    setCurrentPhotoIndex((prevIndex) => 
      prevIndex < album.photos.length - 1 ? prevIndex + 1 : prevIndex
    );
  };

  return (
    <div>
      <SEO
        title={album.title}
        description={album.description || `Photo album: ${album.title}`}
      />
      {/* Album header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">{album.title}</h1>
        <p className="text-gray-600 dark:text-gray-300 mb-2">{album.description}</p>
        <p className="text-sm text-gray-500 dark:text-gray-400">{album.date} • {album.photos.length} photos</p>
      </div>
      
      {/* Photo grid */}
      <div className="gallery-grid">
        {album.photos.map((photo, index) => (
          <div 
            key={photo.id} 
            className="album-card cursor-pointer"
            onClick={() => openLightbox(index)}
          >
            <div className="relative overflow-hidden">
              <LazyImage 
                src={photo.src} 
                alt={photo.caption} 
                className="photo-thumbnail"
              />
            </div>
            <div className="p-3">
              <p className="text-sm font-medium truncate">{photo.caption}</p>
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">{photo.date}</p>
            </div>
          </div>
        ))}
      </div>
      
      {/* Lightbox */}
      {lightboxOpen && (
        <Lightbox 
          photos={album.photos}
          currentIndex={currentPhotoIndex}
          onClose={closeLightbox}
          onPrev={goToPrevPhoto}
          onNext={goToNextPhoto}
        />
      )}
    </div>
  );
}
