import { Helmet } from 'react-helmet-async';

interface SEOProps {
  title?: string;
  description?: string;
  canonicalUrl?: string;
}

export default function SEO({ 
  title = 'PhotoAlbum Gallery', 
  description = 'A beautiful, minimalist photo album gallery to showcase your precious memories.',
  canonicalUrl = ''
}: SEOProps) {
  const fullTitle = title === 'PhotoAlbum Gallery' ? title : `${title} | PhotoAlbum Gallery`;
  
  return (
    <Helmet>
      <title>{fullTitle}</title>
      <meta name="description" content={description} />
      {canonicalUrl && <link rel="canonical" href={canonicalUrl} />}
    </Helmet>
  );
}
