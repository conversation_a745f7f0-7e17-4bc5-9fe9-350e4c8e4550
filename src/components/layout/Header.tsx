import { useState } from 'react';
import { Moon, Sun } from 'lucide-react';

interface HeaderProps {
  isDarkMode: boolean;
  toggleDarkMode: () => void;
}

export default function Header({ isDarkMode, toggleDarkMode }: HeaderProps) {
  return (
    <header className="border-b border-gray-200 dark:border-gray-800">
      <div className="header-container flex justify-between items-center">
        <div className="flex items-center space-x-2">
          <h1 className="text-2xl font-bold">PhotoAlbum</h1>
        </div>
        <nav className="hidden md:flex space-x-6">
          <a href="/" className="text-gray-700 dark:text-gray-200 hover:text-primary">Home</a>
          <a href="/albums" className="text-gray-700 dark:text-gray-200 hover:text-primary">Albums</a>
          <a href="/about" className="text-gray-700 dark:text-gray-200 hover:text-primary">About</a>
        </nav>
        <button
          onClick={toggleDarkMode}
          className="p-2 rounded-full bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-200"
          aria-label="Toggle dark mode"
        >
          {isDarkMode ? <Sun size={20} /> : <Moon size={20} />}
        </button>
      </div>
    </header>
  );
}
