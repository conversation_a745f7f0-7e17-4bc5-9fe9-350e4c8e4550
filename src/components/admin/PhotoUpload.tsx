import { useState, useRef } from 'react';
import { Upload, X, Edit2, Image as ImageIcon } from 'lucide-react';
import { Photo } from '../../contexts/AdminContext';

interface PhotoUploadProps {
  photos: Photo[];
  onChange: (photos: Photo[]) => void;
}

export default function PhotoUpload({ photos, onChange }: PhotoUploadProps) {
  const [dragOver, setDragOver] = useState(false);
  const [editingPhoto, setEditingPhoto] = useState<string | null>(null);
  const [editCaption, setEditCaption] = useState('');
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = (files: FileList | null) => {
    if (!files) return;

    const newPhotos: Photo[] = [];
    
    Array.from(files).forEach((file) => {
      if (file.type.startsWith('image/')) {
        const reader = new FileReader();
        reader.onload = (e) => {
          const photo: Photo = {
            id: `photo-${Date.now()}-${Math.random()}`,
            src: e.target?.result as string,
            caption: file.name.replace(/\.[^/.]+$/, ''), // Remove file extension
            date: new Date().toLocaleDateString(),
            file: file
          };
          newPhotos.push(photo);
          
          // Update photos when all files are processed
          if (newPhotos.length === files.length) {
            onChange([...photos, ...newPhotos]);
          }
        };
        reader.readAsDataURL(file);
      }
    });
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
    handleFileSelect(e.dataTransfer.files);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    handleFileSelect(e.target.files);
    // Reset input value to allow selecting the same file again
    e.target.value = '';
  };

  const removePhoto = (photoId: string) => {
    onChange(photos.filter(photo => photo.id !== photoId));
  };

  const startEditingCaption = (photo: Photo) => {
    setEditingPhoto(photo.id);
    setEditCaption(photo.caption);
  };

  const saveCaption = () => {
    if (editingPhoto) {
      onChange(photos.map(photo => 
        photo.id === editingPhoto 
          ? { ...photo, caption: editCaption.trim() || 'Untitled' }
          : photo
      ));
      setEditingPhoto(null);
      setEditCaption('');
    }
  };

  const cancelEdit = () => {
    setEditingPhoto(null);
    setEditCaption('');
  };

  return (
    <div className="space-y-4">
      {/* Upload Area */}
      <div
        className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
          dragOver
            ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
            : 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500'
        }`}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
      >
        <Upload className="mx-auto h-12 w-12 text-gray-400 mb-4" />
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
          Upload Photos
        </h3>
        <p className="text-gray-600 dark:text-gray-400 mb-4">
          Drag and drop your photos here, or click to select files
        </p>
        <button
          type="button"
          onClick={() => fileInputRef.current?.click()}
          className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
        >
          Select Files
        </button>
        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept="image/*"
          onChange={handleFileInputChange}
          className="hidden"
          aria-label="Select image files to upload"
        />
        <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
          Supports: JPG, PNG, GIF, WebP
        </p>
      </div>

      {/* Photo Grid */}
      {photos.length > 0 && (
        <div>
          <h4 className="text-md font-medium text-gray-900 dark:text-white mb-3">
            Photos ({photos.length})
          </h4>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {photos.map((photo) => (
              <div
                key={photo.id}
                className="relative group bg-white dark:bg-gray-700 rounded-lg overflow-hidden shadow-md"
              >
                <div className="aspect-square">
                  <img
                    src={photo.src}
                    alt={photo.caption}
                    className="w-full h-full object-cover"
                  />
                </div>
                
                {/* Overlay */}
                <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-opacity flex items-center justify-center">
                  <div className="opacity-0 group-hover:opacity-100 transition-opacity flex space-x-2">
                    <button
                      type="button"
                      onClick={() => startEditingCaption(photo)}
                      className="bg-white text-gray-900 p-2 rounded-full hover:bg-gray-100 transition-colors"
                      title="Edit caption"
                    >
                      <Edit2 size={16} />
                    </button>
                    <button
                      type="button"
                      onClick={() => removePhoto(photo.id)}
                      className="bg-red-500 text-white p-2 rounded-full hover:bg-red-600 transition-colors"
                      title="Remove photo"
                    >
                      <X size={16} />
                    </button>
                  </div>
                </div>

                {/* Caption */}
                <div className="p-2">
                  {editingPhoto === photo.id ? (
                    <div className="space-y-2">
                      <input
                        type="text"
                        value={editCaption}
                        onChange={(e) => setEditCaption(e.target.value)}
                        className="w-full px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                        placeholder="Enter caption"
                        autoFocus
                      />
                      <div className="flex space-x-1">
                        <button
                          type="button"
                          onClick={saveCaption}
                          className="flex-1 bg-blue-600 text-white px-2 py-1 rounded text-xs hover:bg-blue-700 transition-colors"
                        >
                          Save
                        </button>
                        <button
                          type="button"
                          onClick={cancelEdit}
                          className="flex-1 bg-gray-500 text-white px-2 py-1 rounded text-xs hover:bg-gray-600 transition-colors"
                        >
                          Cancel
                        </button>
                      </div>
                    </div>
                  ) : (
                    <p className="text-xs text-gray-600 dark:text-gray-300 truncate">
                      {photo.caption}
                    </p>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {photos.length === 0 && (
        <div className="text-center py-8">
          <ImageIcon className="mx-auto h-16 w-16 text-gray-300 dark:text-gray-600 mb-4" />
          <p className="text-gray-500 dark:text-gray-400">
            No photos uploaded yet. Add some photos to get started!
          </p>
        </div>
      )}
    </div>
  );
}
