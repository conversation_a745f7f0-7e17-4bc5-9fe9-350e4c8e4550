import { useState, useEffect } from 'react';
import { useAdmin } from '../../contexts/AdminContext';
import PhotoUpload from './PhotoUpload';
import { Photo } from '../../contexts/AdminContext';

interface AlbumEditorProps {
  albumId: string;
  onSuccess: () => void;
  onCancel: () => void;
}

export default function AlbumEditor({ albumId, onSuccess, onCancel }: AlbumEditorProps) {
  const { getAlbum, updateAlbum } = useAdmin();
  const album = getAlbum(albumId);
  
  const [formData, setFormData] = useState({
    title: '',
    category: '',
    description: '',
    date: ''
  });
  const [photos, setPhotos] = useState<Photo[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    if (album) {
      setFormData({
        title: album.title,
        category: album.category,
        description: album.description || '',
        date: album.date
      });
      setPhotos(album.photos);
    }
  }, [album]);

  if (!album) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500 dark:text-gray-400">Album not found.</p>
        <button
          type="button"
          onClick={onCancel}
          className="mt-4 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
        >
          Go Back
        </button>
      </div>
    );
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handlePhotosChange = (newPhotos: Photo[]) => {
    setPhotos(newPhotos);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.title.trim()) {
      alert('Please enter a title for the album');
      return;
    }

    setIsSubmitting(true);

    try {
      const updatedAlbum = {
        title: formData.title.trim(),
        category: formData.category || 'General',
        description: formData.description.trim(),
        date: formData.date,
        coverImage: photos.length > 0 ? photos[0].src : album.coverImage,
        photos: photos
      };

      updateAlbum(albumId, updatedAlbum);
      onSuccess();
    } catch (error) {
      console.error('Error updating album:', error);
      alert('Failed to update album. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const categories = [
    'Travel',
    'Family',
    'Nature',
    'Urban',
    'Events',
    'Portraits',
    'Architecture',
    'Food',
    'Sports',
    'General'
  ];

  return (
    <div className="max-w-4xl mx-auto">
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Basic Information */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Album Information
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="title" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Album Title *
              </label>
              <input
                type="text"
                id="title"
                name="title"
                value={formData.title}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                placeholder="Enter album title"
                required
              />
            </div>

            <div>
              <label htmlFor="category" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Category
              </label>
              <select
                id="category"
                name="category"
                value={formData.category}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              >
                <option value="">Select a category</option>
                {categories.map(category => (
                  <option key={category} value={category}>{category}</option>
                ))}
              </select>
            </div>

            <div>
              <label htmlFor="date" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Date
              </label>
              <input
                type="text"
                id="date"
                name="date"
                value={formData.date}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                placeholder="e.g., June 2024"
              />
            </div>
          </div>

          <div className="mt-4">
            <label htmlFor="description" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Description
            </label>
            <textarea
              id="description"
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              placeholder="Enter album description (optional)"
            />
          </div>
        </div>

        {/* Photo Management */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Manage Photos
          </h3>
          <PhotoUpload
            photos={photos}
            onChange={handlePhotosChange}
          />
        </div>

        {/* Actions */}
        <div className="flex justify-end space-x-4">
          <button
            type="button"
            onClick={onCancel}
            className="px-6 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
            disabled={isSubmitting}
          >
            Cancel
          </button>
          <button
            type="submit"
            className="px-6 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white rounded-lg transition-colors flex items-center space-x-2"
            disabled={isSubmitting}
          >
            {isSubmitting ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                <span>Updating...</span>
              </>
            ) : (
              <span>Update Album</span>
            )}
          </button>
        </div>
      </form>
    </div>
  );
}
