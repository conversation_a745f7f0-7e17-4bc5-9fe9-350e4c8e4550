import { useState } from 'react';
import { Plus, Edit, Trash2, Upload, Settings } from 'lucide-react';
import { useAdmin } from '../../contexts/AdminContext';
import AlbumForm from './AlbumForm';
import AlbumEditor from './AlbumEditor';

export default function AdminPanel() {
  const { albums, deleteAlbum } = useAdmin();
  const [activeTab, setActiveTab] = useState<'overview' | 'create' | 'edit'>('overview');
  const [editingAlbum, setEditingAlbum] = useState<string | null>(null);

  const handleDeleteAlbum = (albumId: string) => {
    if (window.confirm('Are you sure you want to delete this album? This action cannot be undone.')) {
      deleteAlbum(albumId);
    }
  };

  const handleEditAlbum = (albumId: string) => {
    setEditingAlbum(albumId);
    setActiveTab('edit');
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
            Admin Panel
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Manage your photo albums and upload new content
          </p>
        </div>

        {/* Navigation Tabs */}
        <div className="mb-8">
          <nav className="flex space-x-8 border-b border-gray-200 dark:border-gray-700">
            <button
              type="button"
              onClick={() => setActiveTab('overview')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'overview'
                  ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                  : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
              }`}
            >
              <Settings className="inline-block w-4 h-4 mr-2" />
              Overview
            </button>
            <button
              type="button"
              onClick={() => setActiveTab('create')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'create'
                  ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                  : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
              }`}
            >
              <Plus className="inline-block w-4 h-4 mr-2" />
              Create Album
            </button>
            {editingAlbum && (
              <button
                type="button"
                onClick={() => setActiveTab('edit')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'edit'
                    ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                    : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
                }`}
              >
                <Edit className="inline-block w-4 h-4 mr-2" />
                Edit Album
              </button>
            )}
          </nav>
        </div>

        {/* Tab Content */}
        {activeTab === 'overview' && (
          <div>
            <div className="mb-6 flex justify-between items-center">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                Albums ({albums.length})
              </h2>
              <button
                type="button"
                onClick={() => setActiveTab('create')}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors"
              >
                <Plus size={16} />
                <span>New Album</span>
              </button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {albums.map((album) => (
                <div
                  key={album.id}
                  className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden"
                >
                  <div className="aspect-video bg-gray-200 dark:bg-gray-700 relative">
                    {album.coverImage ? (
                      <img
                        src={album.coverImage}
                        alt={album.title}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center">
                        <Upload className="w-8 h-8 text-gray-400" />
                      </div>
                    )}
                  </div>
                  <div className="p-4">
                    <h3 className="font-semibold text-gray-900 dark:text-white mb-1">
                      {album.title}
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                      {album.photoCount} photos • {album.category}
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-500 mb-3">
                      {album.date}
                    </p>
                    <div className="flex space-x-2">
                      <button
                        type="button"
                        onClick={() => handleEditAlbum(album.id)}
                        className="flex-1 bg-blue-100 hover:bg-blue-200 dark:bg-blue-900 dark:hover:bg-blue-800 text-blue-700 dark:text-blue-300 px-3 py-1 rounded text-sm flex items-center justify-center space-x-1 transition-colors"
                      >
                        <Edit size={14} />
                        <span>Edit</span>
                      </button>
                      <button
                        type="button"
                        onClick={() => handleDeleteAlbum(album.id)}
                        className="flex-1 bg-red-100 hover:bg-red-200 dark:bg-red-900 dark:hover:bg-red-800 text-red-700 dark:text-red-300 px-3 py-1 rounded text-sm flex items-center justify-center space-x-1 transition-colors"
                      >
                        <Trash2 size={14} />
                        <span>Delete</span>
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {activeTab === 'create' && (
          <div>
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">
              Create New Album
            </h2>
            <AlbumForm
              onSuccess={() => setActiveTab('overview')}
              onCancel={() => setActiveTab('overview')}
            />
          </div>
        )}

        {activeTab === 'edit' && editingAlbum && (
          <div>
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">
              Edit Album
            </h2>
            <AlbumEditor
              albumId={editingAlbum}
              onSuccess={() => setActiveTab('overview')}
              onCancel={() => {
                setActiveTab('overview');
                setEditingAlbum(null);
              }}
            />
          </div>
        )}
      </div>
    </div>
  );
}
