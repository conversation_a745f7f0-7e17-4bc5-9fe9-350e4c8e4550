import { useState } from 'react';
import { ChevronLeft, ChevronRight, X } from 'lucide-react';

interface Photo {
  id: string;
  src: string;
  caption: string;
  date: string;
}

interface LightboxProps {
  photos: Photo[];
  currentIndex: number;
  onClose: () => void;
  onPrev: () => void;
  onNext: () => void;
}

export default function Lightbox({ photos, currentIndex, onClose, onPrev, onNext }: LightboxProps) {
  const currentPhoto = photos[currentIndex];

  return (
    <div className="lightbox-overlay" onClick={onClose}>
      <div className="lightbox-content" onClick={(e) => e.stopPropagation()}>
        <button 
          className="absolute top-4 right-4 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-70 transition-opacity"
          onClick={onClose}
          aria-label="Close lightbox"
        >
          <X size={24} />
        </button>
        
        <img 
          src={currentPhoto.src} 
          alt={currentPhoto.caption} 
          className="lightbox-image"
        />
        
        <div className="lightbox-caption">
          <p className="text-lg">{currentPhoto.caption}</p>
          <p className="text-sm text-gray-300 mt-1">{currentPhoto.date}</p>
        </div>
        
        <div className="lightbox-controls">
          <button 
            className="lightbox-button"
            onClick={onPrev}
            disabled={currentIndex === 0}
            aria-label="Previous photo"
          >
            <ChevronLeft size={24} />
          </button>
          
          <button 
            className="lightbox-button"
            onClick={onNext}
            disabled={currentIndex === photos.length - 1}
            aria-label="Next photo"
          >
            <ChevronRight size={24} />
          </button>
        </div>
      </div>
    </div>
  );
}
