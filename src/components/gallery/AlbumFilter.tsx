import { useState } from 'react';
import { Search } from 'lucide-react';

interface AlbumFilterProps {
  onSearch: (query: string) => void;
  onFilterByDate: (date: string) => void;
  availableDates: string[];
}

export default function AlbumFilter({ onSearch, onFilterByDate, availableDates }: AlbumFilterProps) {
  const [searchQuery, setSearchQuery] = useState('');
  
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value;
    setSearchQuery(query);
    onSearch(query);
  };
  
  const handleDateFilterChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    onFilterByDate(e.target.value);
  };
  
  return (
    <div className="flex flex-col md:flex-row gap-4 mb-8">
      <div className="relative flex-grow">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <Search size={18} className="text-gray-400" />
        </div>
        <input
          type="text"
          placeholder="Search albums or photos..."
          className="pl-10 pr-4 py-2 w-full border border-gray-200 dark:border-gray-700 rounded-md bg-white dark:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-primary"
          value={searchQuery}
          onChange={handleSearchChange}
        />
      </div>
      
      <div className="w-full md:w-48">
        <select
          className="w-full px-4 py-2 border border-gray-200 dark:border-gray-700 rounded-md bg-white dark:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-primary"
          onChange={handleDateFilterChange}
          defaultValue=""
        >
          <option value="">All Dates</option>
          {availableDates.map((date) => (
            <option key={date} value={date}>{date}</option>
          ))}
        </select>
      </div>
    </div>
  );
}
