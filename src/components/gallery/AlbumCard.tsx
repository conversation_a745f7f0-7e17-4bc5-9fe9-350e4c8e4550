import LazyImage from '../ui/LazyImage';

interface AlbumCardProps {
  id: string;
  title: string;
  coverImage: string;
  photoCount: number;
  date: string;
}

export default function AlbumCard({ id, title, coverImage, photoCount, date }: AlbumCardProps) {
  return (
    <div className="album-card">
      <a href={`/album/${id}`} className="block">
        <div className="relative overflow-hidden">
          <LazyImage 
            src={coverImage} 
            alt={`${title} album cover`} 
            className="photo-thumbnail"
          />
        </div>
        <div className="p-4">
          <h3 className="font-medium text-lg">{title}</h3>
          <div className="flex justify-between text-sm text-gray-500 dark:text-gray-400 mt-1">
            <span>{photoCount} photos</span>
            <span>{date}</span>
          </div>
        </div>
      </a>
    </div>
  );
}
