import React, { createContext, useContext, useState, useEffect } from 'react';

export interface Photo {
  id: string;
  src: string;
  caption: string;
  date: string;
  file?: File;
}

export interface Album {
  id: string;
  title: string;
  coverImage: string;
  photoCount: number;
  date: string;
  category: string;
  description?: string;
  photos: Photo[];
}

interface AdminContextType {
  albums: Album[];
  addAlbum: (album: Omit<Album, 'id' | 'photoCount'>) => void;
  updateAlbum: (id: string, album: Partial<Album>) => void;
  deleteAlbum: (id: string) => void;
  addPhotoToAlbum: (albumId: string, photo: Omit<Photo, 'id'>) => void;
  updatePhoto: (albumId: string, photoId: string, photo: Partial<Photo>) => void;
  deletePhoto: (albumId: string, photoId: string) => void;
  getAlbum: (id: string) => Album | undefined;
}

const AdminContext = createContext<AdminContextType | undefined>(undefined);

// Initial mock data
const initialAlbums: Album[] = [
  {
    id: 'summer-vacation',
    title: 'Summer Vacation',
    coverImage: '/placeholder-images/summer.jpg',
    photoCount: 24,
    date: 'June 2024',
    category: 'Travel',
    description: 'Memories from our amazing summer trip to the coast.',
    photos: [
      {
        id: 'photo-1',
        src: '/placeholder-images/summer-1.jpg',
        caption: 'Sunset at the beach',
        date: 'June 15, 2024'
      },
      {
        id: 'photo-2',
        src: '/placeholder-images/summer-2.jpg',
        caption: 'Family picnic by the shore',
        date: 'June 16, 2024'
      },
      {
        id: 'photo-3',
        src: '/placeholder-images/summer-3.jpg',
        caption: 'Swimming in the ocean',
        date: 'June 17, 2024'
      }
    ]
  },
  {
    id: 'city-lights',
    title: 'City Lights',
    coverImage: '/placeholder-images/city.jpg',
    photoCount: 18,
    date: 'May 2024',
    category: 'Urban',
    description: 'Exploring the vibrant city nightlife.',
    photos: [
      {
        id: 'photo-4',
        src: '/placeholder-images/city-1.jpg',
        caption: 'Downtown skyline',
        date: 'May 10, 2024'
      },
      {
        id: 'photo-5',
        src: '/placeholder-images/city-2.jpg',
        caption: 'Street photography',
        date: 'May 11, 2024'
      }
    ]
  },
  {
    id: 'nature-walks',
    title: 'Nature Walks',
    coverImage: '/placeholder-images/nature.jpg',
    photoCount: 32,
    date: 'April 2024',
    category: 'Nature',
    description: 'Peaceful walks through beautiful landscapes.',
    photos: [
      {
        id: 'photo-6',
        src: '/placeholder-images/nature-1.jpg',
        caption: 'Mountain trail',
        date: 'April 5, 2024'
      }
    ]
  }
];

export function AdminProvider({ children }: { children: React.ReactNode }) {
  const [albums, setAlbums] = useState<Album[]>(() => {
    const saved = localStorage.getItem('photoAlbums');
    return saved ? JSON.parse(saved) : initialAlbums;
  });

  // Save to localStorage whenever albums change
  useEffect(() => {
    localStorage.setItem('photoAlbums', JSON.stringify(albums));
  }, [albums]);

  const addAlbum = (albumData: Omit<Album, 'id' | 'photoCount'>) => {
    const newAlbum: Album = {
      ...albumData,
      id: `album-${Date.now()}`,
      photoCount: albumData.photos.length,
    };
    setAlbums(prev => [...prev, newAlbum]);
  };

  const updateAlbum = (id: string, albumData: Partial<Album>) => {
    setAlbums(prev => prev.map(album => 
      album.id === id 
        ? { ...album, ...albumData, photoCount: albumData.photos?.length ?? album.photoCount }
        : album
    ));
  };

  const deleteAlbum = (id: string) => {
    setAlbums(prev => prev.filter(album => album.id !== id));
  };

  const addPhotoToAlbum = (albumId: string, photoData: Omit<Photo, 'id'>) => {
    const newPhoto: Photo = {
      ...photoData,
      id: `photo-${Date.now()}`,
    };
    
    setAlbums(prev => prev.map(album => 
      album.id === albumId 
        ? { 
            ...album, 
            photos: [...album.photos, newPhoto],
            photoCount: album.photos.length + 1,
            coverImage: album.photos.length === 0 ? newPhoto.src : album.coverImage
          }
        : album
    ));
  };

  const updatePhoto = (albumId: string, photoId: string, photoData: Partial<Photo>) => {
    setAlbums(prev => prev.map(album => 
      album.id === albumId 
        ? {
            ...album,
            photos: album.photos.map(photo => 
              photo.id === photoId ? { ...photo, ...photoData } : photo
            )
          }
        : album
    ));
  };

  const deletePhoto = (albumId: string, photoId: string) => {
    setAlbums(prev => prev.map(album => 
      album.id === albumId 
        ? {
            ...album,
            photos: album.photos.filter(photo => photo.id !== photoId),
            photoCount: album.photos.length - 1
          }
        : album
    ));
  };

  const getAlbum = (id: string) => {
    return albums.find(album => album.id === id);
  };

  return (
    <AdminContext.Provider value={{
      albums,
      addAlbum,
      updateAlbum,
      deleteAlbum,
      addPhotoToAlbum,
      updatePhoto,
      deletePhoto,
      getAlbum
    }}>
      {children}
    </AdminContext.Provider>
  );
}

export function useAdmin() {
  const context = useContext(AdminContext);
  if (context === undefined) {
    throw new Error('useAdmin must be used within an AdminProvider');
  }
  return context;
}
