import React, { createContext, useContext, useState, useEffect } from 'react';
import { ar, TranslationKeys } from '../locales/ar';

type Language = 'ar';
type Direction = 'rtl' | 'ltr';

interface LanguageContextType {
  language: Language;
  direction: Direction;
  t: TranslationKeys;
  setLanguage: (lang: Language) => void;
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

const translations = {
  ar
};

const directions: Record<Language, Direction> = {
  ar: 'rtl'
};

export function LanguageProvider({ children }: { children: React.ReactNode }) {
  const [language, setLanguage] = useState<Language>(() => {
    const saved = localStorage.getItem('language');
    return (saved as Language) || 'ar';
  });

  const direction = directions[language];
  const t = translations[language];

  useEffect(() => {
    localStorage.setItem('language', language);
    
    // Set document direction and language
    document.documentElement.dir = direction;
    document.documentElement.lang = language;
    
    // Add RTL class to body for styling
    if (direction === 'rtl') {
      document.body.classList.add('rtl');
    } else {
      document.body.classList.remove('rtl');
    }
  }, [language, direction]);

  const handleSetLanguage = (lang: Language) => {
    setLanguage(lang);
  };

  return (
    <LanguageContext.Provider value={{
      language,
      direction,
      t,
      setLanguage: handleSetLanguage
    }}>
      {children}
    </LanguageContext.Provider>
  );
}

export function useLanguage() {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
}

// Helper function for conditional RTL/LTR classes
export function rtlClass(rtlClasses: string, ltrClasses: string = '') {
  return `rtl:${rtlClasses} ltr:${ltrClasses}`;
}

// Helper function for directional spacing
export function directionClass(property: 'ml' | 'mr' | 'pl' | 'pr', value: string) {
  const opposites = {
    ml: 'mr',
    mr: 'ml',
    pl: 'pr',
    pr: 'pl'
  };
  
  return `${property}-${value} rtl:${property}-0 rtl:${opposites[property]}-${value}`;
}
