@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: 0 0% 100%;
  --foreground: 222.2 84% 4.9%;
  --card: 0 0% 100%;
  --card-foreground: 222.2 84% 4.9%;
  --popover: 0 0% 100%;
  --popover-foreground: 222.2 84% 4.9%;
  --primary: 221.2 83.2% 53.3%;
  --primary-foreground: 210 40% 98%;
  --secondary: 210 40% 96.1%;
  --secondary-foreground: 222.2 47.4% 11.2%;
  --muted: 210 40% 96.1%;
  --muted-foreground: 215.4 16.3% 46.9%;
  --accent: 210 40% 96.1%;
  --accent-foreground: 222.2 47.4% 11.2%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 210 40% 98%;
  --border: 214.3 31.8% 91.4%;
  --input: 214.3 31.8% 91.4%;
  --ring: 221.2 83.2% 53.3%;
  --radius: 0.5rem;
}

.dark {
  --background: 222.2 84% 4.9%;
  --foreground: 210 40% 98%;
  --card: 222.2 84% 4.9%;
  --card-foreground: 210 40% 98%;
  --popover: 222.2 84% 4.9%;
  --popover-foreground: 210 40% 98%;
  --primary: 217.2 91.2% 59.8%;
  --primary-foreground: 222.2 47.4% 11.2%;
  --secondary: 217.2 32.6% 17.5%;
  --secondary-foreground: 210 40% 98%;
  --muted: 217.2 32.6% 17.5%;
  --muted-foreground: 215 20.2% 65.1%;
  --accent: 217.2 32.6% 17.5%;
  --accent-foreground: 210 40% 98%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 210 40% 98%;
  --border: 217.2 32.6% 17.5%;
  --input: 217.2 32.6% 17.5%;
  --ring: 224.3 76.3% 48%;
}

* {
  @apply border-border;
}

body {
  @apply bg-background text-foreground;
  font-feature-settings: "rlig" 1, "calt" 1;
}

/* Custom styles for photo gallery */
.gallery-grid {
  @apply grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4;
}

.album-card {
  @apply bg-white dark:bg-gray-800 rounded-lg overflow-hidden shadow-md hover:shadow-lg transition-shadow duration-300;
}

.photo-thumbnail {
  @apply w-full h-48 object-cover transition-transform duration-300 hover:scale-105;
}

.lightbox-overlay {
  @apply fixed inset-0 bg-black bg-opacity-90 z-50 flex items-center justify-center;
}

.lightbox-content {
  @apply max-w-4xl max-h-[90vh] relative;
}

.lightbox-image {
  @apply max-w-full max-h-[80vh] object-contain;
}

.lightbox-caption {
  @apply text-white p-4 text-center;
}

.lightbox-controls {
  @apply absolute inset-x-0 top-1/2 flex justify-between items-center px-4 -translate-y-1/2;
}

.lightbox-button {
  @apply bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-70 transition-opacity;
}

.theme-toggle {
  @apply fixed top-4 right-4 z-10;
}

.header-container {
  @apply container mx-auto px-4 py-8;
}

.main-container {
  @apply container mx-auto px-4 py-6;
}

.footer-container {
  @apply container mx-auto px-4 py-6 mt-8 border-t border-gray-200 dark:border-gray-700;
}
