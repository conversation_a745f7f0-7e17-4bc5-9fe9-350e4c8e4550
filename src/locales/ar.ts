export const ar = {
  // Navigation
  nav: {
    home: 'الرئيسية',
    albums: 'الألبومات',
    admin: 'لوحة التحكم',
    about: 'حول',
    logout: 'تسجيل الخروج'
  },

  // Authentication
  auth: {
    login: 'تسجيل الدخول',
    username: 'اسم المستخدم',
    password: 'كلمة المرور',
    signIn: 'دخول',
    signingIn: 'جاري تسجيل الدخول...',
    invalidCredentials: 'اسم المستخدم أو كلمة المرور غير صحيحة. يرجى المحاولة مرة أخرى.',
    enterUsername: 'أدخل اسم المستخدم',
    enterPassword: 'أدخل كلمة المرور',
    demoCredentials: 'بيانات التجربة:',
    showPassword: 'إظهار كلمة المرور',
    hidePassword: 'إخفاء كلمة المرور',
    adminLogin: 'تسجيل دخول المدير'
  },

  // Home Page
  home: {
    title: 'معرض الصور الشخصي',
    subtitle: 'اكتشف مجموعة رائعة من الذكريات المصورة',
    description: 'استكشف ألبوماتنا المتنوعة من الصور التي تحكي قصصاً جميلة من رحلاتنا ولحظاتنا المميزة.',
    featuredAlbums: 'الألبومات المميزة',
    viewAllAlbums: 'عرض جميع الألبومات',
    photos: 'صورة',
    photo: 'صورة',
    noAlbums: 'لا توجد ألبومات متاحة حالياً.'
  },

  // Albums Page
  albums: {
    title: 'ألبومات الصور',
    description: 'تصفح مجموعتنا من ألبومات الصور التي تضم السفر والعائلة والطبيعة والمزيد.',
    searchPlaceholder: 'البحث في الألبومات...',
    filterByDate: 'تصفية حسب التاريخ',
    filterByCategory: 'تصفية حسب الفئة',
    allDates: 'جميع التواريخ',
    allCategories: 'جميع الفئات',
    showing: 'عرض',
    of: 'من',
    albumsText: 'ألبوم',
    noResults: 'لم يتم العثور على ألبومات تطابق معايير البحث.',
    clearFilters: 'مسح المرشحات'
  },

  // Album Page
  album: {
    notFound: 'الألبوم غير موجود',
    notFoundDescription: 'الألبوم الذي تبحث عنه غير موجود أو تم حذفه.',
    returnHome: 'العودة للرئيسية',
    photos: 'صورة',
    photo: 'صورة'
  },

  // Admin Panel
  admin: {
    title: 'لوحة التحكم',
    description: 'إدارة ألبومات الصور وتحميل محتوى جديد',
    overview: 'نظرة عامة',
    createAlbum: 'إنشاء ألبوم',
    editAlbum: 'تعديل الألبوم',
    newAlbum: 'ألبوم جديد',
    albums: 'ألبوم',
    edit: 'تعديل',
    delete: 'حذف',
    deleteConfirm: 'هل أنت متأكد من حذف هذا الألبوم؟ لا يمكن التراجع عن هذا الإجراء.',
    
    // Album Form
    albumInfo: 'معلومات الألبوم',
    albumTitle: 'عنوان الألبوم',
    albumTitleRequired: 'عنوان الألبوم مطلوب',
    enterTitle: 'أدخل عنوان الألبوم',
    category: 'الفئة',
    selectCategory: 'اختر فئة',
    date: 'التاريخ',
    datePlaceholder: 'مثال: يونيو 2024',
    albumDescription: 'الوصف',
    descriptionPlaceholder: 'أدخل وصف الألبوم (اختياري)',
    photos: 'الصور',
    cancel: 'إلغاء',
    create: 'إنشاء',
    update: 'تحديث',
    creating: 'جاري الإنشاء...',
    updating: 'جاري التحديث...',
    createAlbumBtn: 'إنشاء الألبوم',
    updateAlbumBtn: 'تحديث الألبوم',
    
    // Photo Upload
    uploadPhotos: 'تحميل الصور',
    dragDrop: 'اسحب وأفلت الصور هنا، أو انقر لاختيار الملفات',
    selectFiles: 'اختيار الملفات',
    supportedFormats: 'الصيغ المدعومة: JPG, PNG, GIF, WebP',
    noPhotosYet: 'لم يتم تحميل صور بعد. أضف بعض الصور للبدء!',
    editCaption: 'تعديل التسمية التوضيحية',
    removePhoto: 'إزالة الصورة',
    enterCaption: 'أدخل التسمية التوضيحية',
    save: 'حفظ',
    managePhotos: 'إدارة الصور',
    
    // Validation Messages
    titleRequired: 'يرجى إدخال عنوان للألبوم',
    photosRequired: 'يرجى إضافة صورة واحدة على الأقل للألبوم',
    createError: 'فشل في إنشاء الألبوم. يرجى المحاولة مرة أخرى.',
    updateError: 'فشل في تحديث الألبوم. يرجى المحاولة مرة أخرى.',
    albumNotFound: 'الألبوم غير موجود.',
    goBack: 'العودة'
  },

  // Categories
  categories: {
    travel: 'سفر',
    family: 'عائلة',
    nature: 'طبيعة',
    urban: 'حضري',
    events: 'فعاليات',
    portraits: 'بورتريه',
    architecture: 'عمارة',
    food: 'طعام',
    sports: 'رياضة',
    general: 'عام'
  },

  // About Page
  about: {
    title: 'حول معرض الصور',
    description: 'معرض صور حديث ومتجاوب مصمم لعرض ذكرياتك الجميلة بأفضل طريقة ممكنة.',
    features: 'المميزات',
    feature1: 'تصميم نظيف وأنيق',
    feature2: 'متجاوب مع جميع الأجهزة',
    feature3: 'وضع ليلي ونهاري',
    feature4: 'بحث وتصفية متقدمة',
    feature5: 'لوحة تحكم شاملة',
    feature6: 'تحميل سهل للصور'
  },

  // Common
  common: {
    loading: 'جاري التحميل...',
    error: 'حدث خطأ',
    success: 'تم بنجاح',
    confirm: 'تأكيد',
    close: 'إغلاق',
    next: 'التالي',
    previous: 'السابق',
    search: 'بحث',
    filter: 'تصفية',
    clear: 'مسح',
    all: 'الكل',
    toggleDarkMode: 'تبديل الوضع المظلم',
    somethingWentWrong: 'حدث خطأ ما',
    tryAgain: 'حاول مرة أخرى',
    refreshPage: 'تحديث الصفحة',
    errorDetails: 'تفاصيل الخطأ',
    copyright: 'جميع الحقوق محفوظة',
    photoGallery: 'معرض الصور'
  },

  // Lightbox
  lightbox: {
    closeAlt: 'إغلاق المعرض',
    previousAlt: 'الصورة السابقة',
    nextAlt: 'الصورة التالية',
    imageCounter: 'من'
  }
};

export type TranslationKeys = typeof ar;
