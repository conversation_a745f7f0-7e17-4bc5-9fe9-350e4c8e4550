# Photo Album Gallery Website - User Guide

## Overview

This document provides instructions for using and deploying your new photo album gallery website. The site has been built using React with TypeScript, featuring a clean, minimalistic design that's fully responsive and optimized for all devices.

## Features

- **Clean, Minimalistic Design**: White background with soft shadows for an elegant look
- **Responsive Layout**: Works perfectly on desktop, tablet, and mobile devices
- **Dynamic Gallery Pages**: Each album has its own dedicated page
- **Lightbox View**: Click on any photo to view it in full-screen mode
- **Photo Captions & Dates**: Each photo can display caption and date information
- **Search & Filter**: Find albums by title, category, or date
- **Light/Dark Mode**: Toggle between light and dark themes
- **Optimized Performance**: Lazy loading for fast image loading
- **SEO Ready**: Proper meta tags and semantic HTML structure

## Project Structure

```
photo-album/
├── public/              # Static assets and images
├── src/
│   ├── assets/          # Project assets
│   ├── components/      # React components
│   │   ├── gallery/     # Gallery-specific components
│   │   ├── layout/      # Layout components (header, footer)
│   │   ├── pages/       # Page components
│   │   └── ui/          # UI components
│   ├── App.tsx          # Main application component
│   ├── index.css        # Global styles
│   └── main.tsx         # Entry point
└── package.json         # Dependencies and scripts
```

## How to Use

### Local Development

1. Navigate to the project directory:
   ```
   cd photo-album
   ```

2. Install dependencies:
   ```
   pnpm install
   ```

3. Start the development server:
   ```
   pnpm run dev
   ```

4. Open your browser and visit `http://localhost:5173`

### Adding Your Photos

1. **Organize Your Images**:
   - Place your images in the `public/images` directory
   - Create subdirectories for each album (e.g., `public/images/summer-vacation/`)

2. **Update Album Data**:
   - Edit the album data in the relevant components:
     - For featured albums on homepage: `src/components/pages/HomePage.tsx`
     - For all albums listing: `src/components/pages/AlbumsPage.tsx`
     - For individual album details: `src/components/pages/AlbumPage.tsx`

3. **Image Optimization**:
   - For best performance, optimize your images before adding them
   - Recommended dimensions: 1200px max width for full-size images
   - Thumbnails should be around 400-600px wide

### Deployment

#### Option 1: Netlify

1. Create a Netlify account if you don't have one
2. From the Netlify dashboard, click "New site from Git"
3. Connect to your GitHub/GitLab/Bitbucket repository
4. Set build command to: `pnpm run build`
5. Set publish directory to: `dist`
6. Click "Deploy site"

#### Option 2: GitHub Pages

1. Install gh-pages package:
   ```
   pnpm add -D gh-pages
   ```

2. Add these scripts to package.json:
   ```json
   "predeploy": "pnpm run build",
   "deploy": "gh-pages -d dist"
   ```

3. Deploy with:
   ```
   pnpm run deploy
   ```

## Customization

### Changing Colors

Edit the color variables in `src/index.css` to match your preferred color scheme.

### Modifying Layout

The main layout components are in `src/components/layout/`. You can modify:
- `Header.tsx` - Site navigation and dark mode toggle
- `Footer.tsx` - Footer content and links
- `Layout.tsx` - Overall page structure

### Adding New Pages

1. Create a new component in `src/components/pages/`
2. Add a new route in `src/App.tsx`

## Support

For any questions or issues, please refer to the React and Tailwind CSS documentation:
- React: https://react.dev/
- Tailwind CSS: https://tailwindcss.com/docs

## License

This project is provided for your personal use.
