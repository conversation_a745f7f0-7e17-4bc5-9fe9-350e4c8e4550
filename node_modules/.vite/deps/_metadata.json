{"hash": "ff3108a8", "configHash": "b11c7663", "lockfileHash": "a5fadddd", "browserHash": "b24a8305", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "1a644f49", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "b1786695", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "7a18f4b7", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "f4349218", "needsInterop": true}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "a3f8de60", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "e409fa70", "needsInterop": true}, "react-helmet-async": {"src": "../../react-helmet-async/lib/index.esm.js", "file": "react-helmet-async.js", "fileHash": "4acc01c0", "needsInterop": false}, "react-router-dom": {"src": "../../react-router-dom/dist/index.mjs", "file": "react-router-dom.js", "fileHash": "5005002c", "needsInterop": false}}, "chunks": {"chunk-H6ROQLGZ": {"file": "chunk-H6ROQLGZ.js"}, "chunk-WXTH2UMW": {"file": "chunk-WXTH2UMW.js"}}}