{"hash": "dde2dd0a", "configHash": "b11c7663", "lockfileHash": "21bd8474", "browserHash": "6015ece1", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "85716d01", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "38f22bc8", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "47fd688d", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "c1a51aae", "needsInterop": true}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "e080891e", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "df89e9d6", "needsInterop": true}, "react-helmet-async": {"src": "../../react-helmet-async/lib/index.esm.js", "file": "react-helmet-async.js", "fileHash": "822a2be9", "needsInterop": false}, "react-router-dom": {"src": "../../react-router-dom/dist/index.mjs", "file": "react-router-dom.js", "fileHash": "675bc872", "needsInterop": false}}, "chunks": {"chunk-H6ROQLGZ": {"file": "chunk-H6ROQLGZ.js"}, "chunk-WXTH2UMW": {"file": "chunk-WXTH2UMW.js"}}}